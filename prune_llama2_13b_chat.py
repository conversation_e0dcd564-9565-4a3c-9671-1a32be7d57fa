#!/usr/bin/env python3
"""
Wanda剪枝脚本 - 专门用于Llama-2-13b-chat-hf模型
支持50%非结构化剪枝，包含详细的配置和监控
"""

import argparse
import os
import time
import torch
import numpy as np
from datetime import datetime
from transformers import AutoTokenizer, AutoModelForCausalLM

from lib.prune import prune_wanda, check_sparsity
from lib.eval import eval_ppl, eval_zero_shot

def setup_environment():
    """设置环境和随机种子"""
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    torch.cuda.manual_seed_all(42)
    
    # 打印环境信息
    print("=" * 60)
    print("环境信息:")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    if torch.cuda.is_available():
        print(f"当前GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print("=" * 60)

def load_model(model_path, cache_dir="llm_weights"):
    """加载模型和tokenizer"""
    print(f"正在加载模型: {model_path}")
    start_time = time.time()
    
    # 加载模型
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        cache_dir=cache_dir,
        low_cpu_mem_usage=True,
        device_map="auto"
    )
    
    # 加载tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
    
    # 设置序列长度
    model.seqlen = model.config.max_position_embeddings
    model.eval()
    
    load_time = time.time() - start_time
    print(f"模型加载完成，耗时: {load_time:.2f}秒")
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,} ({total_params/1e9:.2f}B)")
    
    return model, tokenizer

def run_pruning(model, tokenizer, args):
    """执行剪枝过程"""
    print("\n" + "=" * 60)
    print("开始剪枝过程")
    print(f"剪枝方法: {args.prune_method}")
    print(f"稀疏度: {args.sparsity_ratio} ({args.sparsity_ratio*100}%)")
    print(f"剪枝类型: {args.sparsity_type}")
    print("=" * 60)
    
    # 确定设备
    device = torch.device("cuda:0")
    if hasattr(model, 'hf_device_map') and "lm_head" in model.hf_device_map:
        device = model.hf_device_map["lm_head"]
    print(f"使用设备: {device}")
    
    # 执行剪枝
    start_time = time.time()
    
    if args.prune_method == "wanda":
        prune_wanda(args, model, tokenizer, device)
    else:
        raise ValueError(f"不支持的剪枝方法: {args.prune_method}")
    
    prune_time = time.time() - start_time
    print(f"剪枝完成，耗时: {prune_time:.2f}秒")
    
    return prune_time

def evaluate_model(model, tokenizer, args):
    """评估剪枝后的模型"""
    print("\n" + "=" * 60)
    print("开始模型评估")
    print("=" * 60)
    
    # 检查稀疏度
    actual_sparsity = check_sparsity(model)
    print(f"实际稀疏度: {actual_sparsity:.4f} ({actual_sparsity*100:.2f}%)")
    
    # 确定设备
    device = torch.device("cuda:0")
    if hasattr(model, 'hf_device_map') and "lm_head" in model.hf_device_map:
        device = model.hf_device_map["lm_head"]
    
    # 评估困惑度
    print("正在评估WikiText困惑度...")
    ppl_start = time.time()
    ppl_test = eval_ppl(args, model, tokenizer, device)
    ppl_time = time.time() - ppl_start
    print(f"WikiText困惑度: {ppl_test:.4f} (评估耗时: {ppl_time:.2f}秒)")
    
    results = {
        'actual_sparsity': actual_sparsity,
        'ppl_test': ppl_test,
        'ppl_eval_time': ppl_time
    }
    
    # Zero-shot评估（可选）
    if args.eval_zero_shot:
        print("正在进行Zero-shot评估...")
        zs_start = time.time()
        
        task_list = ["boolq", "rte", "hellaswag", "winogrande", "arc_easy", "arc_challenge", "openbookqa"]
        accelerate = True  # 13B模型使用accelerate
        
        zs_results = eval_zero_shot(args.model, model, tokenizer, task_list, 0, accelerate)
        zs_time = time.time() - zs_start
        
        print("Zero-shot评估结果:")
        for task, score in zs_results.items():
            print(f"  {task}: {score:.4f}")
        print(f"Zero-shot评估耗时: {zs_time:.2f}秒")
        
        results['zero_shot'] = zs_results
        results['zero_shot_eval_time'] = zs_time
    
    return results

def save_results(args, results, prune_time):
    """保存结果"""
    # 创建输出目录
    os.makedirs(args.save, exist_ok=True)
    
    # 保存日志
    log_file = os.path.join(args.save, f"log_{args.prune_method}.txt")
    with open(log_file, "w", encoding='utf-8') as f:
        f.write("method\tactual_sparsity\tppl_test\tprune_time\n")
        f.write(f"{args.prune_method}\t{results['actual_sparsity']:.4f}\t{results['ppl_test']:.4f}\t{prune_time:.2f}\n")
        
        if 'zero_shot' in results:
            f.write("\nZero-shot Results:\n")
            for task, score in results['zero_shot'].items():
                f.write(f"{task}\t{score:.4f}\n")
    
    # 保存详细结果
    detail_file = os.path.join(args.save, f"detailed_results_{args.prune_method}.txt")
    with open(detail_file, "w", encoding='utf-8') as f:
        f.write(f"Wanda剪枝详细结果报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"=" * 50 + "\n\n")
        
        f.write(f"模型路径: {args.model}\n")
        f.write(f"剪枝方法: {args.prune_method}\n")
        f.write(f"目标稀疏度: {args.sparsity_ratio:.4f} ({args.sparsity_ratio*100:.2f}%)\n")
        f.write(f"实际稀疏度: {results['actual_sparsity']:.4f} ({results['actual_sparsity']*100:.2f}%)\n")
        f.write(f"剪枝类型: {args.sparsity_type}\n")
        f.write(f"剪枝耗时: {prune_time:.2f}秒\n\n")
        
        f.write(f"WikiText困惑度: {results['ppl_test']:.4f}\n")
        f.write(f"困惑度评估耗时: {results['ppl_eval_time']:.2f}秒\n\n")
        
        if 'zero_shot' in results:
            f.write("Zero-shot评估结果:\n")
            for task, score in results['zero_shot'].items():
                f.write(f"  {task}: {score:.4f}\n")
            f.write(f"Zero-shot评估耗时: {results['zero_shot_eval_time']:.2f}秒\n")
    
    print(f"\n结果已保存到: {args.save}")
    print(f"日志文件: {log_file}")
    print(f"详细结果: {detail_file}")

def main():
    parser = argparse.ArgumentParser(description="Wanda剪枝 - Llama-2-13b-chat-hf")
    
    # 必需参数
    parser.add_argument('--model', type=str, 
                       default='/mnt/sdb/llm_models/Llama-2-13b-chat-hf',
                       help='模型路径')
    
    # 剪枝参数
    parser.add_argument('--prune_method', type=str, default='wanda',
                       choices=['wanda'], help='剪枝方法')
    parser.add_argument('--sparsity_ratio', type=float, default=0.5,
                       help='稀疏度比例')
    parser.add_argument('--sparsity_type', type=str, default='unstructured',
                       choices=['unstructured'], help='剪枝类型')
    
    # 数据参数
    parser.add_argument('--nsamples', type=int, default=128,
                       help='校准数据样本数')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    
    # 输出参数
    parser.add_argument('--save', type=str, 
                       default='out/llama2_13b_chat/unstructured/wanda/',
                       help='结果保存路径')
    parser.add_argument('--save_model', type=str, default=None,
                       help='剪枝后模型保存路径')
    
    # 评估参数
    parser.add_argument('--eval_zero_shot', action='store_true',
                       help='是否进行zero-shot评估')
    
    # 其他参数
    parser.add_argument('--cache_dir', type=str, default='llm_weights',
                       help='模型缓存目录')
    parser.add_argument('--use_variant', action='store_true',
                       help='是否使用Wanda变体')
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    # 加载模型
    model, tokenizer = load_model(args.model, args.cache_dir)
    
    # 执行剪枝
    prune_time = run_pruning(model, tokenizer, args)
    
    # 评估模型
    results = evaluate_model(model, tokenizer, args)
    
    # 保存结果
    save_results(args, results, prune_time)
    
    # 保存剪枝后的模型（可选）
    if args.save_model:
        print(f"\n正在保存剪枝后的模型到: {args.save_model}")
        os.makedirs(args.save_model, exist_ok=True)
        model.save_pretrained(args.save_model)
        tokenizer.save_pretrained(args.save_model)
        print("模型保存完成")
    
    print("\n" + "=" * 60)
    print("剪枝流程全部完成!")
    print("=" * 60)

if __name__ == '__main__':
    main()
