#!/usr/bin/env python3
"""
检查剪枝前后模型大小的脚本
"""
import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

def get_model_size(model_path):
    """获取模型文件大小（以GB为单位）"""
    total_size = 0
    if os.path.isdir(model_path):
        for dirpath, dirnames, filenames in os.walk(model_path):
            for filename in filenames:
                if filename.endswith(('.bin', '.safetensors')):
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
    else:
        total_size = os.path.getsize(model_path)
    
    return total_size / (1024**3)  # 转换为GB

def count_zero_parameters(model):
    """统计模型中零参数的数量"""
    total_params = 0
    zero_params = 0
    
    for name, param in model.named_parameters():
        if 'weight' in name:  # 只统计权重参数
            total_params += param.numel()
            zero_params += (param == 0).sum().item()
    
    return total_params, zero_params, zero_params / total_params

def main():
    # 原始模型路径
    original_model = "/mnt/sdb/llm_models/Llama-2-13b-chat-hf"
    
    # 剪枝后模型路径
    pruned_model = "/mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_50"
    
    print("=== 模型大小对比 ===")
    
    # 检查原始模型大小
    if os.path.exists(original_model):
        original_size = get_model_size(original_model)
        print(f"原始模型大小: {original_size:.2f} GB")
    else:
        print(f"原始模型路径不存在: {original_model}")
    
    # 检查剪枝后模型大小
    if os.path.exists(pruned_model):
        pruned_size = get_model_size(pruned_model)
        print(f"剪枝后模型大小: {pruned_size:.2f} GB")
        
        # 加载剪枝后的模型来检查稀疏性
        print("\n=== 加载模型检查稀疏性 ===")
        try:
            model = AutoModelForCausalLM.from_pretrained(
                pruned_model,
                torch_dtype=torch.float16,
                device_map="auto",
                low_cpu_mem_usage=True
            )
            
            total_params, zero_params, sparsity = count_zero_parameters(model)
            print(f"总参数数量: {total_params:,}")
            print(f"零参数数量: {zero_params:,}")
            print(f"实际稀疏率: {sparsity:.4f} ({sparsity*100:.2f}%)")
            
            # 检查具体某一层的权重
            print("\n=== 检查第一层权重示例 ===")
            first_layer = model.model.layers[0]
            for name, module in first_layer.named_modules():
                if hasattr(module, 'weight') and 'mlp' in name:
                    weight = module.weight.data
                    zero_count = (weight == 0).sum().item()
                    total_count = weight.numel()
                    layer_sparsity = zero_count / total_count
                    print(f"{name}: 稀疏率 {layer_sparsity:.4f} ({zero_count}/{total_count})")
                    break
                    
        except Exception as e:
            print(f"加载模型时出错: {e}")
    else:
        print(f"剪枝后模型路径不存在: {pruned_model}")
    
    print("\n=== 分析结论 ===")
    print("如果剪枝前后模型大小相同，可能的原因：")
    print("1. PyTorch/Transformers的save_pretrained()方法不会压缩稀疏权重")
    print("2. 零权重仍然占用存储空间，只是数值为0")
    print("3. 需要使用专门的稀疏格式（如COO, CSR）来真正减少存储空间")
    print("4. 或者需要手动移除零权重参数")

if __name__ == "__main__":
    main()
