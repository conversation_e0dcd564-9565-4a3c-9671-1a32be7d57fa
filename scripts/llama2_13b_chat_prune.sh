#!/bin/bash

# Wanda剪枝脚本 - Llama-2-13b-chat-hf模型
# 50%非结构化剪枝

# 设置变量
MODEL_PATH="/mnt/sdb/llm_models/Llama-2-13b-chat-hf"
SPARSITY_RATIO=0.5
CUDA_DEVICE=5
OUTPUT_DIR="/mnt/sdb/jjji/Pruned_Models"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 设置CUDA设备
export CUDA_VISIBLE_DEVICES=$CUDA_DEVICE

# 创建输出目录
mkdir -p $OUTPUT_DIR/unstructured/wanda/

echo "=========================================="
echo "开始对Llama-2-13b-chat-hf进行Wanda剪枝"
echo "模型路径: $MODEL_PATH"
echo "稀疏度: ${SPARSITY_RATIO} (50%)"
echo "剪枝类型: 非结构化"
echo "输出目录: $OUTPUT_DIR"
echo "时间戳: $TIMESTAMP"
echo "=========================================="

# 检查模型路径是否存在
if [ ! -d "$MODEL_PATH" ]; then
    echo "错误: 模型路径不存在: $MODEL_PATH"
    exit 1
fi

# 运行Wanda剪枝
echo "正在运行Wanda剪枝..."
python main.py \
    --model $MODEL_PATH \
    --prune_method wanda \
    --sparsity_ratio $SPARSITY_RATIO \
    --sparsity_type unstructured \
    --save $OUTPUT_DIR/unstructured/wanda/ \
    --save_model $OUTPUT_DIR/unstructured/wanda/pruned_model_$TIMESTAMP \
    --eval_zero_shot

# 检查剪枝是否成功
if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "剪枝完成!"
    echo "结果保存在: $OUTPUT_DIR/unstructured/wanda/"
    echo "剪枝后的模型保存在: $OUTPUT_DIR/unstructured/wanda/pruned_model_$TIMESTAMP"
    echo "=========================================="
    
    # 显示结果文件
    echo "生成的文件:"
    ls -la $OUTPUT_DIR/unstructured/wanda/
    
    # 显示日志内容
    if [ -f "$OUTPUT_DIR/unstructured/wanda/log_wanda.txt" ]; then
        echo "剪枝结果摘要:"
        cat $OUTPUT_DIR/unstructured/wanda/log_wanda.txt
    fi
else
    echo "剪枝过程中出现错误!"
    exit 1
fi
