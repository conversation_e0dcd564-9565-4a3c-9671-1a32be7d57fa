#!/bin/bash

# 使用wikitext2数据集进行WANDA剪枝的示例脚本
# 相比c4数据集，wikitext2更小，下载和处理速度更快

CUDA_VISIBLE_DEVICES=6 python main.py \
    --model /mnt/sdb/llm_models/Llama-2-13b-chat-hf \
    --prune_method wanda \
    --sparsity_ratio 0.75 \
    --sparsity_type unstructured \
    --dataset wikitext2 \
    --save /mnt/sdb/jjji/Pruned_Modelsl/lama2_13b_chat/unstructured/wanda_75_wikitext2/ \
    --save_model /mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_75_wikitext2/

echo "剪枝完成！使用了wikitext2数据集进行校准。"
