# Llama-2-13b-chat-hf Wanda剪枝使用指南

本指南提供了使用Wanda算法对Llama-2-13b-chat-hf模型进行50%非结构化剪枝的完整方案。

## 快速开始

### 方法1: 使用bash脚本（推荐）

```bash
# 给脚本添加执行权限
chmod +x scripts/llama2_13b_chat_prune.sh

# 运行剪枝
./scripts/llama2_13b_chat_prune.sh
```

### 方法2: 使用Python脚本

```bash
# 基本用法
python prune_llama2_13b_chat.py

# 带zero-shot评估
python prune_llama2_13b_chat.py --eval_zero_shot

# 保存剪枝后的模型
python prune_llama2_13b_chat.py --save_model out/llama2_13b_chat/pruned_model
```

### 方法3: 直接使用main.py

```bash
python main.py \
    --model /mnt/sdb/llm_models/Llama-2-13b-chat-hf \
    --prune_method wanda \
    --sparsity_ratio 0.5 \
    --sparsity_type unstructured \
    --save out/llama2_13b_chat/unstructured/wanda/ \
    --save_model out/llama2_13b_chat/pruned_model \
    --eval_zero_shot
```

## 参数说明

### 核心参数
- `--model`: 模型路径，默认为 `/mnt/sdb/llm_models/Llama-2-13b-chat-hf`
- `--prune_method`: 剪枝方法，使用 `wanda`
- `--sparsity_ratio`: 稀疏度比例，`0.5` 表示50%剪枝
- `--sparsity_type`: 剪枝类型，`unstructured` 表示非结构化剪枝

### 输出参数
- `--save`: 结果保存目录
- `--save_model`: 剪枝后模型保存路径（可选）

### 评估参数
- `--eval_zero_shot`: 是否进行zero-shot任务评估
- `--nsamples`: 校准数据样本数，默认128

## 系统要求

### 硬件要求
- **GPU内存**: 至少24GB（推荐32GB+）
- **系统内存**: 至少32GB
- **存储空间**: 至少50GB可用空间

### 软件要求
- Python 3.9+
- PyTorch 2.0.1+
- CUDA 11.3+ 或 12.x
- transformers 4.28.0

## 预期结果

根据论文结果，对Llama-2-13b模型进行50%非结构化Wanda剪枝的预期性能：

- **原始模型困惑度**: ~4.57
- **剪枝后困惑度**: ~5.56
- **稀疏度**: 50.00%
- **剪枝时间**: 约10-30分钟（取决于硬件）

## 输出文件

剪枝完成后，会在输出目录生成以下文件：

```
out/llama2_13b_chat/unstructured/wanda/
├── log_wanda.txt                    # 简要结果日志
├── detailed_results_wanda.txt       # 详细结果报告
└── pruned_model_YYYYMMDD_HHMMSS/   # 剪枝后的模型（如果指定保存）
    ├── config.json
    ├── pytorch_model.bin
    ├── tokenizer.json
    └── ...
```

## 故障排除

### 常见问题

1. **GPU内存不足**
   ```
   解决方案: 
   - 确保GPU有足够内存（24GB+）
   - 关闭其他GPU进程
   - 使用更小的batch size
   ```

2. **模型路径错误**
   ```
   解决方案:
   - 检查模型路径是否正确
   - 确保模型文件完整
   - 检查文件权限
   ```

3. **依赖版本冲突**
   ```
   解决方案:
   - 按照INSTALL.md重新安装环境
   - 使用conda环境隔离
   ```

### 性能优化

1. **加速剪枝过程**
   - 使用更少的校准样本（--nsamples 64）
   - 跳过zero-shot评估（不使用--eval_zero_shot）

2. **节省内存**
   - 使用fp16精度（默认已启用）
   - 设置适当的device_map

## 进阶用法

### 批量剪枝不同稀疏度

```bash
# 创建批量剪枝脚本
for sparsity in 0.3 0.4 0.5 0.6; do
    python prune_llama2_13b_chat.py \
        --sparsity_ratio $sparsity \
        --save out/llama2_13b_chat/unstructured/wanda_${sparsity}/ \
        --save_model out/llama2_13b_chat/pruned_model_${sparsity}
done
```

### 自定义评估

```python
# 在Python中使用剪枝后的模型
from transformers import AutoTokenizer, AutoModelForCausalLM

model_path = "out/llama2_13b_chat/pruned_model"
model = AutoModelForCausalLM.from_pretrained(model_path)
tokenizer = AutoTokenizer.from_pretrained(model_path)

# 进行推理
inputs = tokenizer("Hello, how are you?", return_tensors="pt")
outputs = model.generate(**inputs, max_length=50)
response = tokenizer.decode(outputs[0], skip_special_tokens=True)
print(response)
```

## 联系支持

如果遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看项目的GitHub Issues
3. 确保按照INSTALL.md正确安装了环境

## 参考资料

- [Wanda论文](https://arxiv.org/abs/2306.11695)
- [项目主页](https://eric-mingjie.github.io/wanda/home.html)
- [GitHub仓库](https://github.com/locuslab/wanda)
